import { parseJsonMarkdown as _parseJsonMarkdown } from "@langchain/core/output_parsers";
import { jsonrepair } from "jsonrepair";
import logger from "../logger";

/**
 * 从markdown中解析出json，暂时使用`langchain`的实现
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const parseJsonMarkdown = <T = any>(input: string): T | null => {
  if (typeof input !== "string" || !input.trim()) {
    return null;
  }
  try {
    const data = _parseJsonMarkdown(input);
    if (data === null) {
      return parseUnCompletedJsonMarkdown<T>(input);
    }
    return data;
  } catch (error) {
    logger.error("解析json失败", input, error);
    return parseUnCompletedJsonMarkdown<T>(input);
  }
};

/**
 * 解析不完整的json字符串
 */
export const parseUnCompletedJsonMarkdown = <T = unknown>(text: string): null | T => {
  text = text.trim();
  if (!text) {
    return null;
  }
  try {
    const repairText = jsonrepair(text);
    return JSON.parse(repairText);
  } catch {
    return null;
  }
};
