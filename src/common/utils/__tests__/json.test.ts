import { parseJsonMarkdown, parseUnCompletedJsonMarkdown } from "../json";

// Test data - separated from test cases
const TEST_DATA = {
  validJson: '{"name": "<PERSON>", "age": 30}',
  validJsonMarkdown: '```json\n{"name": "<PERSON>"}\n```',
  invalidJson: '{name: "<PERSON>",}',
  emptyString: "",
  unrepairable: "invalid{json",
};

const TEST_CASES = {
  parseJsonMarkdown: [
    {
      name: "should parse valid markdown JSON",
      input: TEST_DATA.validJsonMarkdown,
      mockReturn: { name: "<PERSON>" },
      expected: { name: "<PERSON>" },
    },
    {
      name: "should repair to uncompleted parser when langchain fails",
      input: TEST_DATA.invalidJson,
      expected: { name: "<PERSON>" },
    },
    {
      name: "should return null when both parsers fail",
      input: TEST_DATA.unrepairable,
      expected: null,
    },
  ],

  parseUnCompletedJsonMarkdown: [
    {
      name: "should return null for empty string",
      input: TEST_DATA.emptyString,
      expected: null,
    },
    {
      name: "should parse valid JSON",
      input: TEST_DATA.validJson,
      expected: JSON.parse(TEST_DATA.validJson),
    },
    {
      name: "should repair and parse invalid JSON",
      input: TEST_DATA.invalidJson,
      expected: { name: "John" },
    },
    {
      name: "should return null for unrepairable JSON",
      input: TEST_DATA.unrepairable,
      expected: null,
    },
  ],
};

describe("json utilities", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("parseJsonMarkdown", () => {
    test.each(TEST_CASES.parseJsonMarkdown)("$name", async ({ input, expected }) => {
      const result = parseJsonMarkdown(input);
      expect(result).toEqual(expected);
    });
  });

  describe("parseUnCompletedJsonMarkdown", () => {
    test.each(TEST_CASES.parseUnCompletedJsonMarkdown)("$name", ({ input, expected }) => {
      const result = parseUnCompletedJsonMarkdown(input);
      expect(result).toEqual(expected);
    });
  });
});
