import httpClient from "@/common/clients/http.client";
import { TerminalRes } from "@/interfaces/client";
import { utilsClient } from "./UtilsClient";
import {
  IAppendInquiryParams,
  IAppendProxyInquiryParams,
  ICarModel,
  ICustomInquiryDetailParams,
  IGetInquiryIsAllowAppendResponse,
  InquiryDetailParams,
  InquiryReParameter,
  ITireInquiryVehicleItem,
} from "@/interfaces/inquiry";
import {
  IGetInquiryBasicInfoResponse,
  IGetInquiryExtraInfoV3Response,
  IGetInquiryPromotionsInfoItem,
  IGetPromotionsParams,
  IGetSuitesDetailPayload,
  IGetSuitesDetailResponse,
  IGetTmsLablesPayload,
  IGetTmsLablesResponse,
  IInquiryDetailRes,
  IInquiryInfo,
  IInquiryStateRes,
  IInquiryStoreInfoRes,
} from "../interface";
import {
  ICreateRecommendPlanParams,
  ICreateRecommendPlanRes,
  IGetRecommendPlanParams,
  IGetRecommendPlanRes,
} from "../interface/IRecommendPlan";
import { IGetIntelligentPlanParams, IGetIntelligentPlanRes } from "../interface/IIntelligentPlan";
import { ICreateProxyInquiry } from "../interface/IProxyInquiry";

class InquiryClient {
  // 询价单详情
  public async getInquiryDetailV2(body: InquiryDetailParams) {
    const headers = utilsClient.getHeaders();
    const { inquiryId, source, fromPage, isSentry = false } = body;
    const { result } = await httpClient.get<TerminalRes<IInquiryInfo>>(
      `/terminal-api-v2/inquiries/${inquiryId}/detailV2?platform=${source}&fromPage=${fromPage}&isSentry=${isSentry}`,
      {
        headers,
        body,
      }
    );

    return {
      errorCode: result?.errorCode,
      message: result?.message,
      data: result?.data || {},
    };
  }

  // 询价单详情传入采购员信息
  public async getCustomInquiryDetailV2(body: ICustomInquiryDetailParams) {
    const headers = utilsClient.getHeaders();
    const { inquiryId, source, fromPage, isSentry = false, userLoginId = "", garageCompanyId = "" } = body;
    const { result } = await httpClient.get<TerminalRes<IInquiryInfo>>(
      `/terminal-api-v2/inquiries/customer/${inquiryId}/detailV2?platform=${source}&fromPage=${fromPage}&isSentry=${isSentry}&userLoginId=${userLoginId}&garageCompanyId=${garageCompanyId}`,
      {
        headers,
        body,
      }
    );
    return {
      errorCode: result?.errorCode,
      message: result?.message,
      data: result?.data || {},
    };
  }

  public async createInquiry(body: InquiryReParameter) {
    const headers = utilsClient.getHeaders();
    const { result } = await httpClient.post<
      TerminalRes<{
        inquiryId: string;
        isSimpleDemand?: boolean;
      }>
    >(`/terminal-api-v2/inquiries`, {
      headers,
      body,
    });

    return {
      errorCode: result?.errorCode,
      inquiryId: result?.data?.inquiryId,
      message: result?.message,
    };
  }

  // 代客询价创建询价单
  public async proxyInquiryCreate(body: ICreateProxyInquiry) {
    const headers = utilsClient.getHeaders();
    const { result } = await httpClient.post<string>(`/inquiry-service/inquiry/create/from-prerecord`, {
      headers,
      body,
    });
    return {
      errorCode: null,
      inquiryId: result,
      message: "",
    };
  }

  // 根据vin码获取车型信息
  public async getVehicleModels(vin: string) {
    const headers = utilsClient.getHeaders();
    const { result } = await httpClient.get<TerminalRes<ITireInquiryVehicleItem[]>>(
      `/terminal-api-v2/tyre_inquiry/vehicle/models/${vin}`,
      {
        headers,
      }
    );

    return {
      errorCode: result?.errorCode,
      message: result?.message,
      data: result?.data || [],
    };
  }

  // 追加需求
  public async appendInquiryOes(body: IAppendInquiryParams) {
    const headers = utilsClient.getHeaders();
    const { result } = await httpClient.post<
      TerminalRes<{
        isAppendOe: boolean;
      }>
    >(`/terminal-api-v2/inquiries/append/oes`, {
      headers,
      body,
    });

    return {
      errorCode: result?.errorCode,
      message: result?.message,
      isAppendOe: result?.data?.isAppendOe,
    };
  }

  // 代客询价追加需求
  public async appendProxyInquiryOes(body: IAppendProxyInquiryParams) {
    const headers = utilsClient.getHeaders();
    const { result } = await httpClient.post<
      TerminalRes<{
        isAppendOe: boolean;
      }>
    >(`/terminal-api-v2/inquiries/customer/append/oes`, {
      headers,
      body,
    });

    return {
      errorCode: result?.errorCode,
      message: result?.message,
      isAppendOe: result?.data?.isAppendOe,
    };
  }

  // 解析vin车型
  public async getVinCarModel(vinCode: string) {
    const headers = utilsClient.getHeaders();
    // 短车架号场景需要去掉-
    const vin = vinCode.replace(/-/g, "");
    const { result } = await httpClient.get<TerminalRes<ICarModel[]>>(
      `/terminal-api-v2/inquiries/car_models?vin=${vin}`,
      {
        headers,
      }
    );
    return {
      errorCode: result?.errorCode,
      list: result?.data || [],
      message: result?.message,
    };
  }

  // 获取该vin是否可以追加需求
  public async getInquiryIsAllowAppend(vinCode: string, userLoginId: string, garageCompanyId: string) {
    const headers = utilsClient.getHeaders();
    // 短车架号场景需要去掉-
    const vin = vinCode.replace(/-/g, "");
    let url = `/terminal-api-v2/inquiries/append_by_vin/${vin}/is_allow`;
    if (userLoginId && garageCompanyId) {
      url = `/terminal-api-v2/inquiries/customer/append_by_vin/${vin}/is_allow?userLoginId=${userLoginId}&garageCompanyId=${garageCompanyId}`;
    }
    const { result } = await httpClient.get<TerminalRes<IGetInquiryIsAllowAppendResponse>>(url, {
      headers,
    });
    return {
      errorCode: result?.errorCode,
      data: { ...(result?.data || {}), vinCode: vin },
      message: result?.message,
    };
  }

  /**
   * 查询询价单报价状态
   * @param {String} orderId
   * @returns {Promise}
   */
  public async getInquiryCount(inquiryId: string) {
    const headers = utilsClient.getHeaders();
    const { result } = await httpClient.get<TerminalRes<IInquiryStateRes>>(
      `/terminal-api-v2/ai_chat/inquiry/count/${inquiryId}`,
      {
        headers,
      }
    );
    return {
      errorCode: result?.errorCode,
      data: result?.data || { inquiryCount: 0 },
      message: result?.message,
    };
  }

  // 获取全车件询价单报价详情
  public async getInquiryQuoteDetail(inquiryId: string) {
    const headers = utilsClient.getHeaders();
    const { result } = await httpClient.get<TerminalRes<IInquiryDetailRes>>(
      `/terminal-api-v2/inquiries/${inquiryId}/needsv3`,
      {
        headers,
      }
    );
    return {
      errorCode: result?.errorCode,
      data: result?.data,
      message: result?.message,
    };
  }

  // 获取报价商家详情
  public async getInquiryStoreInfo(inquiryId: string) {
    const headers = utilsClient.getHeaders();
    const { result } = await httpClient.get<TerminalRes<IInquiryStoreInfoRes>>(
      `/terminal-api-v2/inquiries/${inquiryId}/store_info`,
      {
        headers,
      }
    );
    return {
      errorCode: result?.errorCode,
      data: result?.data,
      message: result?.message,
    };
  }

  // 获取询价单基本信息
  public async getInquiryBasicInfo(inquiryId: string) {
    const headers = utilsClient.getHeaders();
    const { result } = await httpClient.get<TerminalRes<IGetInquiryBasicInfoResponse>>(
      `/terminal-api-v2/inquiries/${inquiryId}/basic_info`,
      {
        headers,
      }
    );
    return {
      errorCode: result?.errorCode,
      data: result?.data,
      message: result?.message,
    };
  }

  // 获取包邮和小狮物流信息
  public async getInquiryTmsLabels(body: IGetTmsLablesPayload) {
    const headers = utilsClient.getHeaders();
    const { result } = await httpClient.post<TerminalRes<IGetTmsLablesResponse>>(
      `/terminal-api-v2/inquiries/tms_labels`,
      {
        headers,
        body,
      }
    );

    return {
      errorCode: result?.errorCode,
      message: result?.message,
      data: result?.data,
    };
  }

  // 获取询价单额外信息
  public async getInquiryExtraInfov3(body: IGetTmsLablesPayload) {
    const headers = utilsClient.getHeaders();
    const { result } = await httpClient.post<TerminalRes<IGetInquiryExtraInfoV3Response>>(
      `/terminal-api-v2/inquiries/extra_info_v3`,
      {
        headers,
        body,
      }
    );

    return {
      errorCode: result?.errorCode,
      message: result?.message,
      data: result?.data,
    };
  }

  // 获取促销活动列表
  public async getInquiryPromotionInfo(body: IGetPromotionsParams) {
    const headers = utilsClient.getHeaders();
    const { result } = await httpClient.post<TerminalRes<IGetInquiryPromotionsInfoItem[]>>(
      `/terminal-api-v2/inquiries/promotion_info`,
      {
        headers,
        body,
      }
    );

    return {
      errorCode: result?.errorCode,
      message: result?.message,
      data: result?.data,
    };
  }

  // 查询套件商品明细
  public async getProductsSuites(body: IGetSuitesDetailPayload) {
    const headers = utilsClient.getHeaders();
    const { result } = await httpClient.post<TerminalRes<IGetSuitesDetailResponse>>(
      `/terminal-api-v2/products/suites`,
      {
        headers,
        body,
      }
    );

    return {
      errorCode: result?.errorCode,
      message: result?.message,
      data: result?.data,
    };
  }

  // 开始制作推荐方案
  public async createRecommendPlan(body: ICreateRecommendPlanParams) {
    const res = await httpClient.post<ICreateRecommendPlanRes>(`/inquiry-programme-service/inquiry-programme/produce`, {
      body,
    });
    return { statusCode: res.statusCode, result: res.result };
  }

  // 根据方案组id获取对应方案详情
  public async getPlanDetailById(body: IGetRecommendPlanParams) {
    const res = await httpClient.post<IGetRecommendPlanRes[]>(
      `/inquiry-programme-service/inquiry-programme/details/list-by-group-id`,
      {
        body,
      }
    );
    return { statusCode: res.statusCode, result: res.result };
  }

  public async getIntelligentPlanDetailById(body: IGetIntelligentPlanParams) {
    const res = await httpClient.post<IGetIntelligentPlanRes>(
      `/inquiry-programme-service/inquiry-programme/recommend-contents/detail-by-group-id`,
      {
        body,
      }
    );
    return { statusCode: res.statusCode, result: res.result };
  }
}

export const inquiryClient = new InquiryClient();
