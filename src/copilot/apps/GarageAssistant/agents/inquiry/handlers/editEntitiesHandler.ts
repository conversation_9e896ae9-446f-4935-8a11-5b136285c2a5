import { Intents } from "@/copilot/constants";
import { IHandlerFunction } from "@casstime/copilot-core";
import { FormNames, inquiryForm } from "../forms";
import { inquiryFormEditService } from "../services";
import logger from "@/common/logger";

type EditData = { partNames: string[]; qualities: string[] };

export const editEntitiesHandler: IHandlerFunction = async (context) => {
  const message = context.lastMessage;
  if (message.type === "text" || message.type === "voice") {
    const form = await context.getForm<typeof inquiryForm>(FormNames.inquiryForm);
    const formData = form.getValues();
    const editData = { partNames: formData.partNames || [], qualities: formData.qualities || [] };
    try {
      const actions = await inquiryFormEditService.parseEditActions(editData, message.content || "", context.entities);
      const slots = inquiryFormEditService.applyEditActions(context.slots as EditData, actions);
      context.mergeSlots(slots);
    } catch (error) {
      logger.error("解析或编辑配件出错(EditAction Error)", error);
    }
  }

  return context.next(Intents.inquiry);
};
