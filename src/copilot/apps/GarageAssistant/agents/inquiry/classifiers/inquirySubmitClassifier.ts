import { commandIntents, EditPartNameAction, ScanVinCodeAction } from "@/copilot/constants";
import { stringifyMessage } from "@/common/utils/message";
import { IClassifier, MessageFactory, UNKNOWN_SYMBOL } from "@casstime/copilot-core";
import { FormNames, inquiryForm } from "../forms";
import { InquiryIntents } from "../parsers/inquiryIntentClassifier";
import { IntentCodes } from "../enum";
import logger from "@/common/logger";
import { PromptService } from "@/copilot/services/PromptService";

/**
 * 立即询价分类器,用来判断用户接下来是否需要立即发布询价或者通过序号选择需要询价的配件
 */
export const inquirySubmitClassifier: IClassifier = {
  id: "INQUIRY_HANDLER_SUBMIT_INQUIRY_CLASSIFIER",
  async classify(context) {
    const text = stringifyMessage(context.lastMessage, false).trim();
    const form = context.getForm<typeof inquiryForm>(FormNames.inquiryForm);

    // 判断用户是否没有车架号
    const noVinCode = /(无|没有?|查不到|不知道|看不到|找不到)车架号?/i.test(text);
    if (noVinCode) {
      return IntentCodes.无车架号;
    }

    // 用户输入是否为 0~99 的数字
    const onlyValidNumber = /^\d{1,2}$/.test(text);
    if (onlyValidNumber) {
      const formData = form.getValues();
      const partNames = formData.partNames || [];
      const inputNum = Number(text);
      // 只有一个配件时，用户按1，可能想手动修改配件信息
      if (inputNum === 1 && partNames.length === 1) {
        context.reply(
          MessageFactory.text("如果配件识别不准确，你可以点击下方【修改配件】按钮手动调整", {
            actions: [[EditPartNameAction, ScanVinCodeAction]],
          })
        );
        // 此处抛异常，结束响应流程
        context.break();
      }
      const selectedPartNames = partNames.filter((partName, index) => inputNum === index + 1);
      if (selectedPartNames?.length) {
        context.mergeSlots({
          partNames: selectedPartNames,
        });
        return InquiryIntents.询报价;
      }
    }

    // 用户是否可以一键询价
    const { error } = await form.validate();
    if (error === undefined) {
      const needSubmit =
        /^([没木]有?了?|不要了?|不需要|发布询价|立即询价|一键询价|发布|询价|无|报价|不用|多少钱)$/.test(text);
      if (needSubmit) {
        return commandIntents.IMMEDIATE_INQUIRY;
      }

      // 用llm分析用户意图，返回对应的值
      const allOptions = [
        { intent: "没有其他配件", return: commandIntents.IMMEDIATE_INQUIRY },
        { intent: "不要其他配件", return: commandIntents.IMMEDIATE_INQUIRY },
        { intent: "发布询价", return: commandIntents.IMMEDIATE_INQUIRY },
        { intent: "问价格", return: commandIntents.IMMEDIATE_INQUIRY },
        { intent: "输入配件", return: InquiryIntents.询报价 },
        { intent: "输入VIN码", return: InquiryIntents.询报价 },
        { intent: "不确定", return: UNKNOWN_SYMBOL },
        { intent: "其他", return: UNKNOWN_SYMBOL },
      ];

      const dialog = ["助手: 还有其他配件要一起询价吗？", "用户: " + text].join("\n");
      const promptService = context.getService(PromptService);
      const intent = await promptService.parseUserIntentQuickly(
        dialog,
        allOptions.map((o) => o.intent)
      );
      const option = allOptions.find((o) => o.intent === intent);
      if (option) {
        return option.return;
      }
      logger.warn("inquirySubmitClassifier: 未识别的意图", { intent, text, dialog });
    }

    return UNKNOWN_SYMBOL;
  },
};
