import { Context, IFormConfig, MessageFactory } from "@casstime/copilot-core";
import { SlotRecords } from "@casstime/copilot-core";
import { IAddressItem, ICarModel, IInquiryInvoiceRes, IOriginalItemsItem } from "@/interfaces/inquiry";
import { inquiryService } from "../services/InquiryService";
import { ChannelService } from "../services/ChannelService";
import { SLOTS_CLEARED_FOR_BYE } from "@/copilot/constants/ClearSlots";
import ValidateError, { ValidateErrorCode } from "@/common/error/ValidateError";
import { AI_DISCLAIMER, commandIntents, PARTS_MAX_NUM } from "@/copilot/constants";
import { inquiryClient } from "../clients/InquiryClient";
import { ErrorCode, VersionEnum, FeatureTogglesEnum, AppName } from "@/common/enums";
import { tyreService } from "../services/TyreService";
import { Types } from "mongoose";
import {
  IGetInquiryQuoteRes,
  IInquiryCardInfo,
  IMessageExtra,
  IInquiryTaskParams,
  TaskType,
  IInquiryTyreSpecItem,
} from "../interface";
import { taskService } from "../services/TaskService";
import { trackService } from "../services/TrackService";
import { createObjectId, TyreSpec } from "@/common/utils";
import { config } from "@casstime/apollo-config";
import logger from "@/common/logger";
import { createErrorMessage } from "./factory";
import { isOnlyTyreEntities, isOriginalItemsHasTyreEntities } from "../utils";
import { extractTyreBrands } from "@/common/utils/tyre";
import { getOrCreateReplyMsgId } from "@/copilot/helpers";
import { IDialogueBusinessEnum } from "../enum";
import { IRepairScenariosSlot } from "../interface/IRepariScenarios";
import { renderRichtext } from "@/copilot/richtext";
import { InTheQuotationCard } from "../../richtext/components/IntelligentPlan/InTheQuotationCard";

export interface IOePartNameItem {
  partsNum?: string;
  oeCode?: string;
  oeName?: string;
  partsName: string;
  stdName?: string;
  stdNameCode?: string;
  categoryCode?: string;
  stdInquirySource?: string;
  inquirySource?: string;
  hasResolved?: boolean;
}
export interface InquiryFormData {
  /** 当前vin码 */
  vinCode?: string;
  /** 多个候选vin码 */
  vinCodes?: string[];
  /** 保存vin图片 */
  vinImages?: Record<string, string>;
  /** 当前车型 */
  carModel?: ICarModel;
  /** 已有车型时输入了另一个vin码，前一个的车型 */
  prevCarModel?: ICarModel;
  /** 解析出多车型 */
  carModels?: ICarModel[];
  partNames?: string[];
  qualities?: string[];
  /** 保存配件清单 */
  partNamesImages?: string[];
  /** 用户-发票信息 */
  invoice?: IInquiryInvoiceRes;
  /** 用户-地址信息 */
  address?: IAddressItem;
  /** 用户-终端来源 IOS、ANDROID */
  source?: string;
  /** 用户-页面 */
  fromPage?: string;
  /** 用户-用户名 */
  user?: {
    userId?: string;
    userName: string;
    cellphone: number;
    companyId?: string;
    companyName: string;
    userParentName: string;
    userParentId: string;
  };
  /** 操作人 */
  operator?: {
    userId: string;
    userName: string;
  };
  /** 是否是代理询价 */
  isProxyInquiry?: boolean;
  /** vin已有询价-询价单id */
  inquiryId?: string;
  /** vin已有询价-询价单车型信息 */
  inquiryCarModel?: ICarModel;
  /** vin已有询价-确认追加 */
  appendInquiry?: boolean;
  /** 临时变量：触发发票状态变更 */
  __CHANGEINVOICE__?: boolean;
  /** 临时变量：不再询问是否追加配件
   * true: 已经选择‘追加配件’或‘发布新询价单’。不会再问
   * false: 没有选择，会继续问
   * undefined: 没有追加场景
   */
  __NOASKAPPEND__?: boolean;
  /** 临时变量：上一条消息为报价，则用该报价的车架号追加配件 */
  __ADDITIONALPARTS__?: boolean;

  /** 轮胎规格 */
  originalItems?: (IOriginalItemsItem & IInquiryTyreSpecItem)[];
  /** 订单ids */
  orderIds?: string[];
  /** 询价单ids */
  inquiryIds?: string[];
  /** 重新询价，询价单号 */
  reInquiryId?: string;
  /** 带oe号的配件 */
  oePartNames?: IOePartNameItem[];
}

export enum InquiryFormErrorField {
  VINCODE_PARTNAME = "VINCODE_PARTNAME", // 车架号和零配件
  APPEND_PARTNAME = "APPEND_PARTNAME", // 存在有效询价单场景下的追加配件
  TYRESIZE = "TYRESIZE", // 轮胎规格
  VINCODE = "VINCODE", // 车架号
  MULTIPLE_VINCODE = "MULTIPLE_VINCODE", // 多个车架号
  CHANGE_VINCODE = "CHANGE_VINCODE", // 切换车架号
  CARMODEL = "CARMODEL", // 车型信息
  PARTNAME = "PARTNAME", // 配件
  PARTNAME_SUBMIT = "PARTNAME_SUBMIT", // 配件(可提交询价)
  INVOICE = "INVOICE", // 发票信息
  ADDRESS = "ADDRESS", // 地址信息
  INQUIRYID = "INQUIRYID", // 询价单
  TO_CASS_SERVICE = "TO_CASS_SERVICE", //人工客服
}

function needGetCarModel(formData: InquiryFormData, context: Context) {
  const { lastMessage, slotsChanges } = context;
  const { vinCode: vinCodeObj } = slotsChanges;
  const { vinCode, carModel, __NOASKAPPEND__, reInquiryId } = formData;
  const nluCarModel = lastMessage.nlu?.slots?.carModel;
  // 1-nlu 携带了 carModel，不需要查询车型
  if (nluCarModel) {
    return false;
  }
  // 2-追加场景，不需要查询车型
  if (__NOASKAPPEND__) {
    return false;
  }
  // 重新询价，不需要查车型
  if (reInquiryId) {
    return false;
  }

  // 3-vinCode变化了，需要查询车型
  if (vinCodeObj?.changed) {
    return true;
  }
  // 4-有vinCode，没有车型信息，需要查询车型
  if (vinCode && !carModel) {
    return true;
  }
  return false;
}

export const inquiryForm: IFormConfig<InquiryFormData, { success: boolean; inquiryId?: string; demandId?: string }> = {
  doMapping(slots: SlotRecords) {
    return {
      vinCode: slots["vinCode"],
      vinCodes: slots["vinCodes"],
      carModel: slots["carModel"],
      carModels: slots["carModels"],
      partNames: slots["partNames"],
      qualities: slots["qualities"],
      originalItems: slots["originalItems"],
      invoice: slots["invoice"],
      source: slots["source"],
      address: slots["address"],
      user: slots["user"],
      operator: slots["operator"],
      isProxyInquiry: slots["isProxyInquiry"],
      fromPage: slots["fromPage"],
      inquiryId: slots["inquiryId"],
      inquiryCarModel: slots["inquiryCarModel"],
      appendInquiry: slots["appendInquiry"],
      prevCarModel: slots["prevCarModel"],
      vinImages: slots["vinImages"],
      partNamesImages: slots["partNamesImages"],
      orderIds: slots["orderIds"],
      inquiryIds: slots["inquiryIds"],
      reInquiryId: slots["reInquiryId"],
      __CHANGEINVOICE__: slots["__CHANGEINVOICE__"],
      __NOASKAPPEND__: slots["__NOASKAPPEND__"],
      __ADDITIONALPARTS__: slots["__ADDITIONALPARTS__"],
      oePartNames: slots["oePartNames"],
    };
  },
  /**
   * 表单校验
   */
  async doValidate(formData, context) {
    let { inquiryId, inquiryCarModel, partNames, carModel, invoice } = formData;
    const { vinCode, vinCodes, address, user, source, originalItems = [] } = formData;
    const { payload, slotsChanges, entities } = context;
    const { vinCode: vinCodeObj, partNames: partNamesObj } = slotsChanges;
    const isLessThan0518 = context.payload.appVersion.isLessThan(VersionEnum.FIVE_18_0);
    let needNoVinCodeTyreTip = false; // 需要提示车架号没有对应轮胎规格

    // 初始化发票信息
    if (!invoice) {
      invoice = {
        openInvoiceType: "NO" as const,
        isRequireItemInvoice: false,
      };
      context.mergeSlots({ invoice });
    }

    // 只输入轮胎规格,且已经解析对应的轮胎规格，跳过校验
    const onlyTyreEntities = isOnlyTyreEntities(entities);
    const originalItemsHasTyreEntities = isOriginalItemsHasTyreEntities(entities, originalItems);
    if (onlyTyreEntities && originalItemsHasTyreEntities && address) return;

    // 获取通道类型
    const channelService = context.getService(ChannelService);
    const [dialogue, sceneEnabled] = await Promise.all([channelService.getDialogue(), channelService.getSceneEnable()]);
    const businessId = dialogue?.businessId;
    const airConditionerInfo = businessId === IDialogueBusinessEnum.AIR_CONDITIONER ? "空调相关信息" : undefined;

    const vinCodeLength = vinCode?.length || 0;
    if (vinCodeLength === 16 || vinCodeLength === 18) {
      // 避免疑似车架号替换掉了已有的车架号
      const beforeVinCodeLength = vinCodeObj.before?.length;
      if (beforeVinCodeLength && beforeVinCodeLength !== 16 && beforeVinCodeLength !== 18) {
        context.mergeSlots({ vinCode: vinCodeObj.before });
      }
      const noPartNameEntity = !entities?.filter((entity) => entity.name === "partName").length;
      if (noPartNameEntity) {
        // 报错：16或者18位车架号，提示修改
        throw ValidateError.create<{ key: string; errorVinCode?: string }>(
          ValidateErrorCode.LENGTH_ERROR,
          `你输入的【${vinCode}】似乎是车架号，有${vinCodeLength}位，但我们只支持17位车架号的解析。你可以点击下方按钮回填输入框进行修改，或者作为零件号输入`,
          {
            key: InquiryFormErrorField.VINCODE,
            errorVinCode: vinCode,
          }
        );
      }
    }

    if (vinCodes?.length) {
      // 报错：多个候选车架号，让用户选择
      throw ValidateError.create<{ key: string }>(ValidateErrorCode.DUPLICATE_VALUE, "识别到多个车架号，请选择一个", {
        key: InquiryFormErrorField.MULTIPLE_VINCODE,
      });
    }
    // 0、没有车架号和零配件信息
    if (!partNames?.length && !vinCode) {
      context.clearSlots(["__CHANGEINVOICE__"]);
      // 报错：引导输入车架号和零配件信息
      throw ValidateError.create<{ key: string }>(
        ValidateErrorCode.MISSING_REQUIRED_FIELD,
        "告诉我车架号和配件，帮你问价",
        {
          key: InquiryFormErrorField.VINCODE_PARTNAME,
        }
      );
    }
    // 1、输入配件
    if (partNamesObj?.changed) {
      // 提取轮胎规格
      const { partNamesNew, originalItems, noVinCodeTyre } = await tyreService.handleTyreSize(formData, payload);
      needNoVinCodeTyreTip = noVinCodeTyre;
      partNames = partNamesNew;
      context.mergeSlots({
        partNames,
        originalItems,
      });
      if (partNamesNew.length > PARTS_MAX_NUM) {
        partNames = partNamesNew.slice(0, PARTS_MAX_NUM);
        context.mergeSlots({
          partNames,
        });
        // 报错：超过配件限定值
        throw ValidateError.create<{ key: string }>(
          ValidateErrorCode.LENGTH_ERROR,
          `单次询价录入配件不得超过${PARTS_MAX_NUM}个，如需录入更多，请联系客户经理`,
          {
            key: vinCode ? InquiryFormErrorField.PARTNAME_SUBMIT : InquiryFormErrorField.PARTNAME,
          }
        );
      }
    }
    // 2、输入车架号
    const isNeedGetCarModel = needGetCarModel(formData, context);
    if (isNeedGetCarModel) {
      // (1)车架号是否存在未过期询价单
      const [{ data: inquiryItem }, carModelResult] = await Promise.all([
        inquiryClient.getInquiryIsAllowAppend(vinCode || "", user?.userId || "", user?.companyId || ""),
        inquiryClient.getVinCarModel(vinCode || ""),
      ]);
      if (inquiryItem?.isAllowAppend) {
        const carModelInfo = carModelResult?.list?.[0] || {};
        inquiryId = inquiryItem.inquiryId;
        inquiryCarModel = inquiryItem;
        carModel = Object.keys(carModelInfo).length ? carModelInfo : inquiryItem;
        context.mergeSlots({
          inquiryId,
          __NOASKAPPEND__: false,
          inquiryCarModel,
          carModel,
        });
        // 报错：是否追加需求
        throw ValidateError.create<{ key: string }>(
          ValidateErrorCode.DUPLICATE_VALUE,
          `当前VIN码已存在询价单（${inquiryItem.inquiryId}），是否在原询价单追加？`,
          { key: InquiryFormErrorField.INQUIRYID }
        );
      } else {
        // 清除之前的询价单信息
        context.clearSlots(["inquiryId", "inquiryCarModel"]);
      }
      // (2)查询车型信息
      const { errorCode: carModelErrorCode, list: carModelNameRes } = await inquiryClient.getVinCarModel(vinCode || "");
      if (carModelNameRes.length === 1) {
        if (carModelNameRes[0].garageBrandInquiryState !== "GARAGE_OWN_INQUIRY_BRANDS") {
          // 报错：不支持该车架号
          if (vinCodeObj.before) {
            context.mergeSlots({ vinCode: vinCodeObj.before });
          }
          throw ValidateError.create<{ key: string }>(
            ValidateErrorCode.INVALID_VALUE_TYPE,
            "暂不支持该品牌，努力完善中～",
            { key: InquiryFormErrorField.VINCODE }
          );
        }
        // (2.1)单车型
        // 确认车型后处理 partNames
        const { partNamesNew, originalItems, noVinCodeTyre } = await tyreService.handleTyreSize(formData, payload);
        needNoVinCodeTyreTip = noVinCodeTyre;
        const prevCarModel = { ...carModel };
        carModel = carModelNameRes[0];
        partNames = partNamesNew;
        context.mergeSlots({
          carModel,
          prevCarModel,
          partNames,
          originalItems,
        });
        const repairScenarios = context.slots.repairScenarios as IRepairScenariosSlot;
        if (
          carModel &&
          carModel.vinCode !== repairScenarios?.vinCode &&
          !repairScenarios?.accidentCode &&
          sceneEnabled
        ) {
          // 没有选择过维修场景，查询事故场景
          const accidents = await inquiryService.getRepairScenarios({
            partNames: partNames || [],
            brandCode: carModel.carBrandCode || carModel.carBrandId || "",
            vehicleTypeClass: carModel.vehicleTypeClass || "",
            prompt: airConditionerInfo,
          });
          context.mergeSlots({
            repairScenarios: {
              ...repairScenarios,
              vinCode: carModel.vinCode,
              accidents: accidents,
            },
          });
        }
        // 存在已有车架号
        const hasPrevCarModel = Boolean(Object.keys(prevCarModel)?.length);
        if (vinCodeObj.before && hasPrevCarModel) {
          // 报错：是否切换车架号
          throw ValidateError.create<{ key: string }>(
            ValidateErrorCode.DUPLICATE_VALUE,
            "您发了新的车架号给我，要用新车架号询价，还是继续使用原来的？",
            { key: InquiryFormErrorField.CHANGE_VINCODE }
          );
        }
      } else if (carModelNameRes.length > 1) {
        // (2.2)多车型, 判断是否有车型历史记录
        const carBrandId = context.lastMessage.extra?.vinRecord?.carBrandId;
        if (carBrandId && carModelNameRes.some((item) => item.carBrandCode === carBrandId)) {
          carModel = carModelNameRes.find((item) => item.carBrandCode === carBrandId);
          // 确认车型后处理 partNames
          const { partNamesNew, originalItems, noVinCodeTyre } = await tyreService.handleTyreSize(formData, payload);
          needNoVinCodeTyreTip = noVinCodeTyre;
          partNames = partNamesNew;
          context.mergeSlots({
            carModel,
            partNames,
            originalItems,
          });
        } else {
          context.mergeSlots({
            carModels: carModelNameRes,
          });
          // 报错：多车型场景
          throw ValidateError.create<{ key: string }>(ValidateErrorCode.DUPLICATE_VALUE, "请选择车型", {
            key: InquiryFormErrorField.CARMODEL,
          });
        }
      } else {
        // (3)解析车型失败
        context.clearSlots(["vinCode"]);
        let errorMsg = "";
        switch (carModelErrorCode) {
          case ErrorCode.UNSUPPORTED_BRAND:
            errorMsg = `抱歉，当前系统暂未收录该车架号【${vinCode}】，请修改后重新发送给我`;
            break;
          case ErrorCode.ERROR_VIN:
            errorMsg = `识别到【${vinCode}】不是有效的VIN码，麻烦核对后重新发送`;
            break;
          default:
            errorMsg = `抱歉，未能解析出该车架号【${vinCode}】完整的车型信息，请重新发送车架号给我`;
            break;
        }
        if (!vinCode) {
          errorMsg = "抱歉，请发送新的车架号给我";
        }
        // 报错：解析车型失败
        throw ValidateError.create<{ key: string; errorVinCode?: string }>(
          ValidateErrorCode.MISSING_REQUIRED_FIELD,
          errorMsg,
          {
            key: InquiryFormErrorField.CARMODEL,
            errorVinCode: vinCode || "",
          }
        );
      }
    }

    const repairScenarios = context.slots.repairScenarios as IRepairScenariosSlot;
    if (carModel && carModel.vinCode !== repairScenarios?.vinCode && !repairScenarios?.accidentCode && sceneEnabled) {
      // 没有选择过维修场景，查询事故场景
      const accidents = await inquiryService.getRepairScenarios({
        partNames: partNames || [],
        brandCode: carModel.carBrandCode || carModel.carBrandId || "",
        vehicleTypeClass: carModel.vehicleTypeClass || "",
        prompt: airConditionerInfo,
      });
      context.mergeSlots({
        repairScenarios: {
          ...repairScenarios,
          vinCode: carModel.vinCode,
          accidents: accidents,
        },
      });
    }
    // 3、追加询价校验
    if (inquiryId && !partNames?.length) {
      // 报错：没有追加的配件
      throw ValidateError.create<{ key: string }>(ValidateErrorCode.MISSING_REQUIRED_FIELD, "您想追加什么配件", {
        key: InquiryFormErrorField.APPEND_PARTNAME,
      });
    }
    // 4、配件是否只有轮胎
    const onlyTyre = partNames?.join("") === "轮胎";
    if (onlyTyre) {
      // 报错：缺少轮胎规格
      const needTyreMessage = `${
        needNoVinCodeTyreTip ? "非常抱歉，该车架号没有查询到对应的轮胎规格，请" : ""
      }告诉我轮胎规格，或拍轮胎图帮您问价`;
      throw ValidateError.create<{ key: string }>(ValidateErrorCode.MISSING_REQUIRED_FIELD, needTyreMessage, {
        key: InquiryFormErrorField.TYRESIZE,
      });
    }
    // 5、校验车架号，单独轮胎询价场景不校验
    const isOnlyTyreInquiry = tyreService.isAllTyreSpec(partNames);
    if (!isOnlyTyreInquiry && (!vinCode || !(carModel || inquiryCarModel))) {
      const errorMessage = isLessThan0518
        ? '发送<font color="red">车架号</font>即可发起询价'
        : `告诉我车架号，帮您问价`;
      // 报错：缺少车架号
      throw ValidateError.create<{ key: string }>(ValidateErrorCode.MISSING_REQUIRED_FIELD, errorMessage, {
        key: InquiryFormErrorField.VINCODE,
      });
    }
    // 6、校验配件
    if (!partNames?.length) {
      const errorMessage = isLessThan0518
        ? '<font color="#646566" fontSize="28">您需要购买什么配件呢？</font>'
        : `您想要为${carModel?.carBrandName || inquiryCarModel?.carBrandName}买什么配件？`;
      // 报错：缺少配件
      throw ValidateError.create<{ key: string }>(ValidateErrorCode.MISSING_REQUIRED_FIELD, errorMessage, {
        key: InquiryFormErrorField.PARTNAME,
      });
    }
    // 7、校验发票信息，单独轮胎询价场景不校验
    if (!isOnlyTyreInquiry && Object.keys(invoice || {}).length < 1) {
      // 报错：缺少发票信息
      throw ValidateError.create<{ key: string }>(ValidateErrorCode.MISSING_REQUIRED_FIELD, "请问需要发票吗？", {
        key: InquiryFormErrorField.INVOICE,
      });
    }
    // 8、校验地址信息
    if (Object.keys(address || {}).length < 1)
      // 报错：缺少地址信息
      throw ValidateError.create<{ key: string }>(
        ValidateErrorCode.MISSING_REQUIRED_FIELD,
        "请点击下方按钮，添加收货地址",
        { key: InquiryFormErrorField.ADDRESS }
      );
    // 9、其他必要询价信息
    if (Object.keys(user || {}).length < 1 || !source) {
      // 报错：缺少询价信息
      throw ValidateError.create(ValidateErrorCode.MISSING_REQUIRED_FIELD, "询价基本信息有误，请联系人工客服", {
        key: InquiryFormErrorField.TO_CASS_SERVICE,
      });
    }
  },
  doSubmit: async function (formData: InquiryFormData, context: Context) {
    const { payload, lastMessage, entities } = context;
    const onlyTyreEntities = isOnlyTyreEntities(entities);
    // 获取通道类型
    const channelService = context.getService(ChannelService);
    const dialogue = await channelService.getDialogue();
    const businessId = dialogue?.businessId || "";
    const { inquiryDetail, tyreInquiryDemand, inquiryId, demandId, message, errorCode } =
      await inquiryService.inquiryPublish(formData, onlyTyreEntities, businessId);
    if (!inquiryId && !demandId) {
      if (errorCode === 605) {
        context.reply(MessageFactory.text("目前系统中暂未找到你输入的轮胎规格呢(•́︿•̀｡)，建议你核对后重新输入"));
        // 去掉表单中轮胎询价信息
        const partNames = (formData?.partNames || []).filter((partName) => !TyreSpec.includesTyreSpec(partName));
        context.mergeSlots({
          partNames,
          originalItems: [],
        });
      } else {
        context.reply(createErrorMessage(message || "询价发布失败，请重试"));
        context.clearSlots(SLOTS_CLEARED_FOR_BYE);
      }
      context.done();
      return { success: false };
    }

    const inquiryCardInfo: IInquiryCardInfo = {
      ...inquiryDetail,
      vinImage: formData.vinImages?.[formData.vinCode || ""],
      partNamesImages: formData.partNamesImages,
      vin: formData.vinCode || "",
      carModel: formData.carModel,
      tyreInquiryDemand,
    };

    // 发布询价埋点
    const trackMessages = trackService.inquiryPubliceTrack(inquiryCardInfo);
    trackMessages.forEach((trackMessage) => context.reply(trackMessage));

    const noSupportRichText = payload.appVersion?.isLessThan(VersionEnum.FIVE_18_0);
    let needInquiryPolling = true; // 是否需要发送轮询消息
    if (noSupportRichText) {
      context.reply(inquiryService.getInquiryFormMessage(inquiryCardInfo));
    } else {
      // 发布询价成功场景 - 领取询价优惠券
      if (!formData.inquiryId) {
        const inquiryCouponsMessage = await inquiryService.grantInquiryCoupons(payload.companyId);
        if (inquiryCouponsMessage) {
          context.reply(MessageFactory.text(inquiryCouponsMessage.text, inquiryCouponsMessage.others));
        }
      }

      const { app, companyId, data } = payload;
      const { owner = "", fromUser, dialogueId } = data;

      // 查询当前是否有轮询任务正在进行
      const tasks = await taskService.getTaskPolling({ owner, dialogueId, done: false, taskType: TaskType.INQUIRY });
      // 如果没有，则需要约定后台轮询消息
      if (!tasks.length) {
        needInquiryPolling = true;
      }

      // 是否专属客服试点
      const isA9tAirforceEnter = await inquiryService.getIsPilotArea(
        payload.companyId,
        FeatureTogglesEnum.A9T_AIRFORCE_ENTER
      );

      // 轮胎询价单轮询任务
      if (demandId) {
        // 发送轮胎询价卡片
        const params: Partial<IInquiryTaskParams> = {};
        const extra: IMessageExtra = {};
        const messageId = getOrCreateReplyMsgId(context);
        const factory = MessageFactory.with({ id: messageId });
        extra.demandId = demandId;
        const embed = inquiryService.createEmptyQuoteRichText({}, tyreInquiryDemand, false, isA9tAirforceEnter);
        context.reply(
          factory.text("正在为您报价，请稍等…", {
            embed,
            extra,
          })
        );

        const tyreQuote = {
          ...tyreInquiryDemand,
          demandId,
        };
        const textContent = "content" in lastMessage ? lastMessage.content : undefined;
        const brandName = extractTyreBrands(textContent || "")[0]?.name;
        if (brandName) {
          tyreQuote.originalItems = tyreQuote.originalItems?.map((item) => {
            item.brandName = brandName;
            return item;
          });
        }
        params.messageId = messageId;
        params.expiration = (tyreQuote.createdDate || new Date().getTime()) + 24 * 60 * 60 * 1000;
        params.tyreQuote = tyreQuote;
        params.demandId = demandId;
        params.tyreNeedPolling = true;
        // 保存代客询价相关信息
        params.isProxyInquiry = formData.isProxyInquiry || false;
        params.userLoginId = formData.user?.userId || "";
        params.garageCompanyId = formData.user?.companyId || "";
        const inquiryTask = {
          taskId: demandId,
          taskType: TaskType.INQUIRY,
          owner,
          app,
          companyId,
          userId: fromUser,
          params,
          done: false,
        };
        // 更新或者新建轮询任务
        await taskService.findOneAndUpdateTaskPolling(
          { owner, taskId: demandId, done: false, dialogueId },
          inquiryTask
        );
      }

      // 全车件询价单轮询任务
      if (inquiryId) {
        // 发送全车件询价卡片
        const embed = {
          type: "richtext",
          content: renderRichtext(InTheQuotationCard, {
            disableAction: false,
            inquiryDetail: inquiryCardInfo,
          }),
        };
        const params: Partial<IInquiryTaskParams> = {};
        const extra: IMessageExtra = {};

        let messageId = "";
        if (lastMessage.type === "image") {
          // 如果是图片消息，则使用上下文的消息id
          messageId = getOrCreateReplyMsgId(context);
        } else {
          messageId = createObjectId();
        }

        const factory = MessageFactory.with({ id: messageId });
        const inquiryExpiration = new Date().getTime() + 24 * 60 * 60 * 1000;
        extra.inquiryId = inquiryId;
        extra.vinCode = formData.vinCode || "";
        extra.carModel = formData.carModel;
        context.reply(
          factory.text("正在为您报价，请稍等…", {
            embed,
            extra,
          })
        );

        // const channelService = context.getService(ChannelService);
        // const dialogue = await channelService.getDialogue();
        // const businessId = dialogue?.businessId;
        // if (businessId === IDialogueBusinessEnum.AIR_CONDITIONER) {
        //   const airforceMessageId = getOrCreateReplyMsgId(context);
        //   const airforceFactory = MessageFactory.with({ id: airforceMessageId });
        //   const { vinCode, carModel } = formData;
        //   const prompt =
        //     `## 车型信息\n` +
        //     `vin：${vinCode}\n` +
        //     `车品牌：${carModel?.saleModelName || carModel?.carBrandName}\n` +
        //     `## 配件清单\n` +
        //     `${formData.partNames?.map((part, index) => `${index + 1}. ${part}`).join("\n")}`;
        //   let content = "";
        //   let hasStarted = false;

        //   // 设置流式回复模式
        //   context.setReplyMode("stream");

        //   // 定时发送"整理资料中..."提示（3秒后开始，每3秒一次）
        //   const interval = setInterval(() => {
        //     if (!hasStarted) {
        //       context.reply(airforceFactory.text("请稍等，整理资料中...", { id: airforceMessageId }));
        //     }
        //   }, 1000);

        //   try {
        //     await inquiryService.getAirConditionerExpertStream(
        //       prompt,
        //       context,
        //       (chunk) => {
        //         // 第一次收到内容时停止定时提示
        //         if (!hasStarted) {
        //           hasStarted = true;
        //           clearInterval(interval);
        //         }

        //         // 累积内容
        //         content += chunk;
        //         context.reply(airforceFactory.markdown(content));
        //       },
        //       () => {
        //         // 全部内容收集完毕，组织完整的markdown格式
        //         context.reply(airforceFactory.markdown(content, { disclaimer: AI_DISCLAIMER }));
        //       }
        //     );
        //   } catch (error) {
        //     logger.error(`获取空调安装指南流式内容出错：${error}`);
        //   } finally {
        //     clearInterval(interval);
        //   }
        // }

        // 默认走推荐方案
        let activeRecommendPlan = true;
        try {
          activeRecommendPlan = !config.get("BACK_QUOTE_PLAN");
        } catch (e) {
          logger.info("查询阿波罗配置 BACK_QUOTE_PLAN 失败", e);
        }

        if (activeRecommendPlan) {
          if (app !== AppName.ProxyInquiry) {
            needInquiryPolling = false;
          }
          // 推荐方案任务配置
          const recommendTask = {
            taskId: inquiryId,
            taskType: TaskType.INTELLIGENT_PLAN,
            owner,
            app,
            companyId,
            userId: fromUser,
            done: false,
            params: {
              inquiryId,
              source: formData.source || "",
              fromPage: formData.fromPage || "",
              isOpenInvoice: formData?.invoice?.openInvoiceType === "YES",
              messageId,
              hasSentTurn: 0,
              turns: 0,
              expiration: inquiryExpiration,
              inquiryDetail,
            },
          };
          await taskService.findOneAndUpdateTaskPolling(
            { owner, taskId: inquiryId, done: false, dialogueId },
            recommendTask
          );
        } else {
          // 更新或者新建轮询任务
          const inquiryQuote: IGetInquiryQuoteRes = {
            inquiryDetail: inquiryCardInfo,
            inquiryNeedPolling: true,
            quoteStoreList: [],
            inquiryId,
          };
          params.messageId = messageId;
          params.expiration = inquiryExpiration;
          params.inquiryQuote = inquiryQuote;
          params.inquiryId = inquiryId;
          params.inquiryCount = 0;
          params.inquiryNeedPolling = true;
          params.source = formData.source || "";
          params.fromPage = formData.fromPage || "";
          params.isOpenInvoice = formData?.invoice?.openInvoiceType === "YES";
          const inquiryTask = {
            taskId: inquiryId,
            taskType: TaskType.INQUIRY,
            owner,
            app,
            companyId,
            userId: fromUser,
            params,
            done: false,
          };
          await taskService.findOneAndUpdateTaskPolling(
            { owner, taskId: inquiryId, done: false, dialogueId },
            inquiryTask
          );
        }
      }
    }

    if (demandId) {
      // 轮胎询价后，去掉轮胎询价信息
      const partNames = (formData?.partNames || []).filter((partName) => !TyreSpec.includesTyreSpec(partName));
      context.mergeSlots({
        partNames,
        originalItems: [],
      });
    }
    if (inquiryId) {
      // 发布后清空上下文
      context.clearSlots(SLOTS_CLEARED_FOR_BYE);
    }

    if (needInquiryPolling) {
      context.reply(
        MessageFactory.command(
          commandIntents.inquiryPolling,
          {},
          {
            background: true,
            reply: {
              type: "command",
              command: commandIntents.inquiryPolling,
              background: true,
              replyDelay: 10 * 1000,
              params: {},
            },
          }
        )
      );
    }

    return { success: true, inquiryId, demandId };
  },
};
